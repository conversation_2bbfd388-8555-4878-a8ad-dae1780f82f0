package com.integral;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import java.io.FileReader;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
public class BulkCancel {
    public static void main(String[] args) {
        if (args.length != 1) {
            System.err.println("Usage: java BulkCancel <csv_file_path>");
            System.err.println("Example: java BulkCancel /path/to/trades.csv");
            System.exit(1);
        }

        String csvFilePath = args[0];
        BulkCancel bulkCancel = new BulkCancel();
        bulkCancel.cancelAllTrades(csvFilePath);
    }

    /**
     * Cancels all trades by reading from a CSV file and posting rejection requests
     * @param csvFilePath Path to the CSV file containing trade data
     */
    public void cancelAllTrades(String csvFilePath) {
        System.out.println("Starting bulk trade cancellation from file: " + csvFilePath);

        try (FileReader fileReader = new FileReader(csvFilePath);
             CSVParser csvParser = new CSVParser(fileReader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {

            int processedCount = 0;
            int successCount = 0;
            int errorCount = 0;

            for (CSVRecord csvRecord : csvParser) {
                try {
                    // Extract required fields from CSV
                    String dealId = csvRecord.get("Deal ID");
                    String user = csvRecord.get("User");
                    String customer = csvRecord.get("Customer");
                    String usdAmount = csvRecord.get("USD Amount");

                    // Format the request body according to the specified format
                    String requestBody = buildRequestBody(dealId, user, customer, "0.0");

                    // Send POST request to cancel the trade
                    boolean success = sendCancelRequest(requestBody);

                    if (success) {
                        successCount++;
                        System.out.println("Successfully cancelled trade with Deal ID: " + dealId);
                    } else {
                        errorCount++;
                        System.err.println("Failed to cancel trade with Deal ID: " + dealId);
                    }

                    processedCount++;

                } catch (Exception e) {
                    errorCount++;
                    System.err.println("Error processing record " + (processedCount + 1) + ": " + e.getMessage());
                }
            }

            System.out.println("Bulk cancellation completed:");
            System.out.println("Total processed: " + processedCount);
            System.out.println("Successful: " + successCount);
            System.out.println("Errors: " + errorCount);

        } catch (IOException e) {
            System.err.println("Error reading CSV file: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Builds the request body in the required format
     * @param dealId Deal ID from CSV
     * @param user User from CSV
     * @param customer Customer from CSV
     * @return Formatted request body string
     */
    private String buildRequestBody(String dealId, String user, String customer) {
        // Format: "valuesRequiredOnIS=650150748553903~MT5Live1@PROIUXM#108358.1_2025-09-01*Not done,&dealAction=Reject"
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());

        StringBuilder requestBody = new StringBuilder();
        requestBody.append("valuesRequiredOnIS=")
                   .append(dealId)
                   .append("~")
                   .append(user)
                   .append("@")
                   .append(customer)
                   .append("#")
                   .append("0.0")
                   .append("_")
                   .append("2025-09-01")
                   .append("*Not done,&dealAction=Reject");

        return requestBody.toString();
    }

    /**
     * Sends HTTP POST request to cancel a trade
     * @param requestBody The formatted request body
     * @return true if successful, false otherwise
     */
    private boolean sendCancelRequest(String requestBody) {
        DefaultHttpClient httpClient = new DefaultHttpClient();
        try {
            String url = "http://localhost:8080/isClient/integral/admin/pendingDeals/PendingDealUpdateIS.jsp";
            HttpPost postRequest = new HttpPost(url);

            // Set content type for form data
            postRequest.setHeader("Content-Type", "application/x-www-form-urlencoded");

            // Set the request body
            StringEntity entity = new StringEntity(requestBody);
            postRequest.setEntity(entity);

            // Execute the request
            HttpResponse response = httpClient.execute(postRequest);

            // Check response status
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            if (statusCode == 200) {
                System.out.println("Request successful. Response: " + responseBody);
                return true;
            } else {
                System.err.println("Request failed with status code: " + statusCode);
                System.err.println("Response: " + responseBody);
                return false;
            }

        } catch (Exception e) {
            System.err.println("Error sending cancel request: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            httpClient.getConnectionManager().shutdown();
        }
    }

}